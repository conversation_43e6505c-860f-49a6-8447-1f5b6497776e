<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">Account</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);"><PERSON></a></li>
            <li class="breadcrumb-item active">Payment</li>
          </ol>
        </div>
        <h4 class="page-title">Weaver Payment</h4>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <h4 class="header-title mb-3"><PERSON> Ledger - Payment</h4>
          
          <!-- Selection Form -->
          <form [formGroup]="paymentForm" class="row g-3 mb-4">
            <div class="col-md-4">
              <label for="weaver" class="form-label">Select Weaver</label>
              <select
                class="form-select"
                id="weaver"
                formControlName="weaver">
                <option value="">Choose Weaver...</option>
                <option *ngFor="let weaver of weavers" [value]="weaver._id">
                  {{ weaver.code }}-{{ weaver.name }}
                </option>
              </select>
            </div>
            
            <!-- <div class="col-md-6">
              <label for="date" class="form-label">Select Date</label>
              <input 
                type="date" 
                class="form-control" 
                id="date" 
                formControlName="date"
                (change)="onFormChange()">
            </div> -->

            <div class="col-md-4">
              <label for="fromDate" class="form-label">From Date</label>
              <input
                type="date"
                class="form-control"
                id="fromDate"
                formControlName="fromDate">
            </div>

            <div class="col-md-4">
              <label for="toDate" class="form-label">To Date</label>
              <input
                type="date"
                class="form-control"
                id="toDate"
                formControlName="toDate">
            </div>

            <div class="col-md-12 mt-3">
              <button
                type="button"
                class="btn btn-primary"
                [disabled]="!paymentForm.get('weaver')?.value || !paymentForm.get('fromDate')?.value || !paymentForm.get('toDate')?.value || isLoading"
                (click)="onLoadData()">
                <i class="bx bx-search me-2"></i>
                <span *ngIf="!isLoading">Load Ledger</span>
                <span *ngIf="isLoading">
                  <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                  Loading...
                </span>
              </button>
            </div>
          </form>

          <!-- Loading Spinner -->
          <div *ngIf="isLoading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>

          <!-- Ledger Table -->
          <div *ngIf="showTable && !isLoading" class="mt-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <h5 class="mb-0">
                Weaver: {{ getSelectedWeaverName() }} |
                Period: {{ paymentForm.get('fromDate')?.value | date:'dd-MM-yyyy' }} to {{ paymentForm.get('toDate')?.value | date:'dd-MM-yyyy' }}
              </h5>
            </div>

            <div class="table-responsive">
              <table class="table table-bordered table-striped">
                <thead class="table-dark">
                  <tr>
                    <th>Sr. No</th>
                    <th>Date</th>
                    <th>Carpet No</th>
                    <th>Credit</th>
                    <th>Debit</th>
                    <th>Balance</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let entry of ledgerEntries; let i = index">
                    <td>{{ i + 1 }}</td>
                    <td>{{ entry.date | date:'dd-MM-yyyy' }}</td>
                    <td>{{ entry.carpetNo || '-' }}</td>
                    <td class="text-success text-end">{{ entry.cr > 0 ? (entry.cr | currency:'INR':'symbol':'1.2-2') : '-' }}</td>
                    <td class="text-danger text-end">{{ entry.dr > 0 ? (entry.dr | currency:'INR':'symbol':'1.2-2') : '-' }}</td>
                    <td class="text-end" [class]="entry.balance >= 0 ? 'text-success' : 'text-danger'">
                      {{ entry.balance | currency:'INR':'symbol':'1.2-2' }}
                    </td>
                  </tr>
                  
                  <!-- Totals Row -->
                  <tr class="table-warning fw-bold">
                    <td colspan="3" class="text-center">Total</td>
                    <td class="text-end">{{ getTotalCR() | currency:'INR':'symbol':'1.2-2' }}</td>
                    <td class="text-end">{{ getTotalDR() | currency:'INR':'symbol':'1.2-2' }}</td>
                    <td class="text-end">{{ getFinalBalance() | currency:'INR':'symbol':'1.2-2' }}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- No Data Message -->
            <div *ngIf="ledgerEntries.length === 0" class="text-center py-4">
              <div class="alert alert-info">
                <i class="bx bx-info-circle me-2"></i>
                No carpet data found for the selected weaver and date range.
              </div>
            </div>
          </div>

          <!-- Instructions -->
          <div *ngIf="!showTable && !isLoading" class="text-center py-4">
            <div class="alert alert-light">
              <i class="bx bx-info-circle me-2"></i>
              Please select weaver, from date, and to date to view the ledger entries.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
