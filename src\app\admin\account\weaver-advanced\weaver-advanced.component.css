.table-responsive {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table {
  margin-bottom: 0;
}

.table th {
  background-color: #495057;
  color: white;
  font-weight: 600;
  text-align: center;
  vertical-align: middle;
  border: none;
  padding: 12px 8px;
}

.table td {
  vertical-align: middle;
  padding: 10px 8px;
  border-color: #dee2e6;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 123, 255, 0.05);
}

.table-warning {
  background-color: #fff3cd !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-success {
  color: #198754 !important;
}

.form-select, .form-control {
  border-radius: 6px;
  border: 1px solid #ced4da;
  padding: 8px 12px;
}

.form-select:focus, .form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.card {
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-body {
  padding: 2rem;
}

.header-title {
  color: #495057;
  font-weight: 600;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  border-radius: 6px;
  padding: 8px 16px;
  font-weight: 500;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-primary:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
}

.alert {
  border-radius: 8px;
  border: none;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.alert-light {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

.text-end {
  text-align: right !important;
}

.breadcrumb {
  background-color: transparent;
  padding: 0;
  margin: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: #6c757d;
}

.page-title {
  color: #495057;
  font-weight: 600;
  margin-bottom: 0;
}

.table tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }
  
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .table th, .table td {
    padding: 8px 4px;
  }
  
  .btn-primary {
    padding: 6px 12px;
    font-size: 0.875rem;
  }
}
