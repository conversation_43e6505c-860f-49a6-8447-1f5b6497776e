import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';

interface WeaverData {
  _id: string;
  name: string;
  code: string;
  H: string;
  source: string; // 'master' or 'received'
}

interface CarpetData {
  _id: string;
  receiveNo: string;
  receivingDate: string;
  amount: string;
  area: string;
  pcs: number;
  issueNo: any;
  weaverNumber: any;
  quality: string;
  design: string;
  H: string;
  weaverName: string;
}

interface LedgerEntry {
  srNo: number;
  date: string;
  carpetNo: string;
  code: string;
  H: string;
  cr: number;    // Credit amount (carpet received)
  dr: number;    // Debit amount (payments/advances)
  balance: number;
}

@Component({
  selector: 'app-weaver-payment',
  templateUrl: './weaver-payment.component.html',
  styleUrls: ['./weaver-payment.component.css']
})
export class WeaverPaymentComponent implements OnInit {
  paymentForm: FormGroup;
  weavers: WeaverData[] = [];
  carpetData: CarpetData[] = [];
  ledgerEntries: LedgerEntry[] = [];
  isLoading = false;
  showTable = false;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient
  ) {
    this.paymentForm = this.fb.group({
      weaver: ['', Validators.required],
      fromDate: ['', Validators.required],
      toDate: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadWeavers();
  }

  loadWeavers(): void {
    console.log('Loading weavers from both sources...');

    // Load from master (weaver employees)
    const masterWeavers$ = this.http.get<any[]>('http://localhost:2000/api/phase-three/weaver/employees');

    // Load from carpet received
    const receivedWeavers$ = this.http.get<any[]>('http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied');

    // Load master weavers first
    masterWeavers$.subscribe({
      next: (masterData) => {
        console.log('Master weavers loaded:', masterData);

        // Transform master data
        const masterWeavers: WeaverData[] = masterData.map(weaver => ({
          _id: weaver._id,
          name: weaver.name.startsWith('K-') ? weaver.name.substring(2) : weaver.name, // Remove K- if already present
          code: 'K',
          H: '',
          source: 'master'
        }));

        // Load carpet received data
        receivedWeavers$.subscribe({
          next: (receivedData) => {
            console.log('Carpet received data loaded:', receivedData);

            // Extract unique weavers from carpet received data
            const uniqueReceivedWeavers = new Map();
            console.log('Sample carpet data:', receivedData.slice(0, 3)); // Debug first 3 records

            receivedData.forEach((carpet, index) => {
              // Try multiple ways to get weaver info
              let weaverId = null;
              let weaverName = null;

              if (carpet.weaverNumber && carpet.weaverNumber._id) {
                weaverId = carpet.weaverNumber._id;
                weaverName = carpet.weaverNumber.name;
              } else if (carpet.weaverNumber && typeof carpet.weaverNumber === 'string') {
                weaverId = carpet.weaverNumber;
                weaverName = carpet.weaverName || `Weaver-${carpet.weaverNumber}`;
              } else if (carpet.weaverName) {
                weaverId = carpet.weaverName.replace(/\s+/g, '_'); // Use name as ID
                weaverName = carpet.weaverName;
              } else if (carpet.receiveNo) {
                // Use receiveNo as fallback
                weaverId = `weaver_${carpet.receiveNo}`;
                weaverName = `Weaver ${carpet.receiveNo}`;
              }

              if (weaverId && weaverName && !uniqueReceivedWeavers.has(weaverId)) {
                uniqueReceivedWeavers.set(weaverId, {
                  _id: weaverId,
                  name: weaverName.startsWith('H-') ? weaverName.substring(2) : weaverName,
                  code: 'H',
                  H: carpet.H || carpet.quality || '',
                  source: 'received'
                });
              }

              // Debug first few records
              if (index < 5) {
                console.log(`Carpet ${index}:`, {
                  weaverNumber: carpet.weaverNumber,
                  weaverName: carpet.weaverName,
                  receiveNo: carpet.receiveNo,
                  extractedId: weaverId,
                  extractedName: weaverName
                });
              }
            });

            const receivedWeavers: WeaverData[] = Array.from(uniqueReceivedWeavers.values());

            // Combine both sources (avoid duplicates by name, not ID)
            const allWeavers = [...masterWeavers];
            receivedWeavers.forEach(receivedWeaver => {
              if (!allWeavers.find(w => w.name.toLowerCase() === receivedWeaver.name.toLowerCase())) {
                allWeavers.push(receivedWeaver);
              }
            });

            this.weavers = allWeavers;
            console.log('Combined weavers:', this.weavers);
          },
          error: (error) => {
            console.error('Error loading carpet received data:', error);
            // Use only master weavers if carpet data fails
            this.weavers = masterWeavers;
          }
        });
      },
      error: (error) => {
        console.error('Error loading master weavers:', error);
        // Try to load only from carpet received if master fails
        receivedWeavers$.subscribe({
          next: (receivedData) => {
            const uniqueReceivedWeavers = new Map();
            receivedData.forEach(carpet => {
              if (carpet.weaverNumber && carpet.weaverNumber._id) {
                const weaverId = carpet.weaverNumber._id;
                if (!uniqueReceivedWeavers.has(weaverId)) {
                  uniqueReceivedWeavers.set(weaverId, {
                    _id: weaverId,
                    name: carpet.weaverNumber.name || carpet.weaverName || 'Unknown',
                    code: carpet.receiveNo || `H-${weaverId.slice(-6)}`,
                    H: carpet.H || carpet.quality || '',
                    source: 'received'
                  });
                }
              }
            });
            this.weavers = Array.from(uniqueReceivedWeavers.values());
          },
          error: (error2) => {
            console.error('Error loading from both sources:', error2);
          }
        });
      }
    });
  }

  onFormChange(): void {
    // Reset table when form changes
    this.showTable = false;
    this.ledgerEntries = [];
  }

  onLoadData(): void {
    const weaverId = this.paymentForm.get('weaver')?.value;
    const fromDate = this.paymentForm.get('fromDate')?.value;
    const toDate = this.paymentForm.get('toDate')?.value;

    if (weaverId && fromDate && toDate) {
      console.log('Loading data for weaver:', weaverId, 'from:', fromDate, 'to:', toDate);

      // Debug: Check selected weaver details
      const selectedWeaver = this.weavers.find(w => w._id === weaverId);
      console.log('Selected weaver details:', selectedWeaver);

      this.loadCarpetData(weaverId, fromDate, toDate);
    }
  }

  loadCarpetData(weaverId: string, fromDate: string, toDate: string): void {
    this.isLoading = true;

    this.http.get<CarpetData[]>('http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied')
      .subscribe({
        next: (data) => {
          console.log('Total carpets received:', data.length);
          console.log('Looking for weaver ID:', weaverId);
          console.log('Date range:', fromDate, 'to', toDate);

          // Filter carpets by weaver and date range
          const filteredCarpets = data.filter(carpet => {
            const carpetDate = new Date(carpet.receivingDate);
            const fromDateObj = new Date(fromDate);
            const toDateObj = new Date(toDate);

            // Enhanced weaver matching logic
            let weaverMatches = false;
            if (carpet.weaverNumber && carpet.weaverNumber._id === weaverId) {
              weaverMatches = true;
            } else if (carpet.weaverNumber && typeof carpet.weaverNumber === 'string' && carpet.weaverNumber === weaverId) {
              weaverMatches = true;
            } else if (carpet.weaverName && carpet.weaverName.replace(/\s+/g, '_') === weaverId) {
              weaverMatches = true;
            } else if (carpet.receiveNo && `weaver_${carpet.receiveNo}` === weaverId) {
              weaverMatches = true;
            }

            // Check if date is within range
            const dateInRange = carpetDate >= fromDateObj && carpetDate <= toDateObj;

            // Debug logging for first few carpets
            if (data.indexOf(carpet) < 3) {
              console.log('Carpet sample:', {
                weaverNumber: carpet.weaverNumber,
                weaverName: carpet.weaverName,
                receivingDate: carpet.receivingDate,
                weaverMatches,
                dateInRange
              });
            }

            return weaverMatches && dateInRange;
          });

          console.log('Filtered carpets found:', filteredCarpets.length);

          this.generateLedgerEntries(filteredCarpets);
          this.isLoading = false;
          this.showTable = true;
        },
        error: (error) => {
          console.error('Error loading carpet data:', error);
          this.isLoading = false;
        }
      });
  }

  generateLedgerEntries(carpets: CarpetData[]): void {
    this.ledgerEntries = [];
    let runningBalance = 0;

    carpets.forEach((carpet, index) => {
      const amount = parseFloat(carpet.amount) || 0;
      runningBalance += amount;

      this.ledgerEntries.push({
        srNo: index + 1,
        date: this.formatDate(carpet.receivingDate),
        carpetNo: carpet.receiveNo || '',
        code: carpet.receiveNo || `K-${carpet._id?.slice(-6) || ''}`,
        H: carpet.H || carpet.quality || '',
        cr: parseFloat(carpet.amount) || 0,
        dr: 0,
        balance: runningBalance
      });
    });
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  }

  getTotalCR(): number {
    return this.ledgerEntries.reduce((sum, entry) => sum + entry.cr, 0);
  }

  getTotalDR(): number {
    return this.ledgerEntries.reduce((sum, entry) => sum + entry.dr, 0);
  }

  getFinalBalance(): number {
    return this.ledgerEntries.length > 0 ? 
      this.ledgerEntries[this.ledgerEntries.length - 1].balance : 0;
  }

  getSelectedWeaverName(): string {
    const weaverId = this.paymentForm.get('weaver')?.value;
    const weaver = this.weavers.find(w => w._id === weaverId);
    return weaver ? weaver.name : '';
  }
}
