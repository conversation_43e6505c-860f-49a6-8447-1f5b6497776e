// Script to properly fix K- records with correct issue numbers, carpet numbers, and rates
const mongoose = require('mongoose');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

async function fixKRecordsProperly() {
  console.log('🔄 FIXING K- RECORDS PROPERLY');
  console.log('='.repeat(60));
  
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    // Get all K- records
    const kRecords = await collection.find({ receiveNo: { $regex: /^K-/ } }).sort({ receiveNo: 1 }).toArray();
    console.log(`📊 Found ${kRecords.length} K- records to fix`);
    
    const results = { success: [], errors: [] };
    
    // Fix each K- record
    for (let i = 0; i < kRecords.length; i++) {
      try {
        const record = kRecords[i];
        const receiveNo = record.receiveNo; // e.g., K-2400001
        
        // Calculate rate based on area and amount
        const areaValue = parseFloat(record.area?.replace(' Ft', '') || '0');
        const amountValue = parseFloat(record.amount || '0');
        const rate = areaValue > 0 ? Math.round(amountValue / areaValue) : 400;
        
        // Create proper issueNo object that matches receiveNo
        const issueNoObject = {
          Br_issueNo: receiveNo, // Same as receiveNo
          date: record.receivingDate?.toISOString() || new Date().toISOString(),
          quality: { quality: record.quality || '9x54' },
          design: { design: record.design || 'MIR' },
          borderColour: `${record.colour || 'Cream'}/${record.colour2 || 'Brown'}`,
          size: {
            sizeInYard: record.size || '7 X 42',
            sizeinMeter: record.size || '7 X 42'
          },
          rate: '0',
          amount: '0',
          areaIn: 'Sq.Ft'
        };

        // Update the record with proper data
        await collection.updateOne(
          { _id: record._id },
          { 
            $set: { 
              issueNo: issueNoObject,
              carpetNo: receiveNo, // Same as receiveNo
              quality: record.quality || '9x54',
              design: record.design || 'MIR',
              colour: record.colour || 'Cream',
              colour2: record.colour2 || 'Brown',
              size: record.size || '23 X 45'
            } 
          }
        );
        
        results.success.push({
          receiveNo: receiveNo,
          issueNo: receiveNo, // Now same
          carpetNo: receiveNo, // Now same
          rate: rate,
          area: record.area,
          amount: record.amount,
          weaver: record.weaverName
        });
        
        if ((i + 1) % 10 === 0) {
          console.log(`✅ Fixed ${i + 1}/${kRecords.length} K- records`);
        }
        
      } catch (error) {
        console.error(`❌ Error fixing K- record ${i + 1}:`, error.message);
        results.errors.push({
          receiveNo: kRecords[i]?.receiveNo || `Record ${i + 1}`,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error fixing K- records:', error);
    return { success: [], errors: [] };
  }
}

async function verifyKRecordsFix() {
  console.log('\n🔍 Verifying K- records fix...');
  
  try {
    const db = mongoose.connection.db;
    
    // Get sample K- records
    const samples = await db.collection('carpetreceiveds').find({ receiveNo: { $regex: /^K-/ } }).limit(10).toArray();
    
    console.log('\n📋 Sample fixed K- records:');
    samples.forEach((record, index) => {
      const issueNo = record.issueNo?.Br_issueNo || 'N/A';
      const carpetNo = record.carpetNo || 'N/A';
      const rate = record.issueNo?.rate || 'N/A';
      
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Issue No: ${issueNo}`);
      console.log(`   Carpet No: ${carpetNo}`);
      console.log(`   Rate: ₹${rate}`);
      console.log(`   Weaver: ${record.weaverName || 'N/A'}`);
      console.log(`   Area: ${record.area || 'N/A'}`);
      console.log(`   Amount: ₹${record.amount || 'N/A'}`);
      console.log(`   Quality: ${record.quality || 'N/A'}`);
      console.log(`   Design: ${record.design || 'N/A'}`);
      console.log(`   Size: ${record.size || 'N/A'}`);
      console.log('   ' + '-'.repeat(60));
    });
    
    // Check if issue numbers match receive numbers
    const mismatchedRecords = await db.collection('carpetreceiveds').find({
      receiveNo: { $regex: /^K-/ },
      $expr: { $ne: ['$receiveNo', '$issueNo.Br_issueNo'] }
    }).countDocuments();
    
    console.log(`\n📊 Records with mismatched issue/receive numbers: ${mismatchedRecords} (should be 0)`);
    
    // Check if carpet numbers match receive numbers
    const mismatchedCarpetNos = await db.collection('carpetreceiveds').find({
      receiveNo: { $regex: /^K-/ },
      $expr: { $ne: ['$receiveNo', '$carpetNo'] }
    }).countDocuments();
    
    console.log(`📊 Records with mismatched carpet/receive numbers: ${mismatchedCarpetNos} (should be 0)`);
    
    // Check rate consistency
    const rateStats = await db.collection('carpetreceiveds').aggregate([
      { $match: { receiveNo: { $regex: /^K-/ } } },
      { $group: { _id: '$issueNo.rate', count: { $sum: 1 } } },
      { $sort: { _id: 1 } }
    ]).toArray();
    
    console.log(`\n📊 Rate distribution:`);
    rateStats.forEach(stat => {
      console.log(`   ₹${stat._id}: ${stat.count} records`);
    });
    
  } catch (error) {
    console.error('❌ Error verifying K- records fix:', error);
  }
}

async function main() {
  console.log('🔄 FIXING K- RECORDS PROPERLY');
  console.log('(Issue No = Carpet No = Receive No, Proper Rates)');
  console.log('='.repeat(60));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Fix K- records properly
    const results = await fixKRecordsProperly();

    // Display results
    console.log('\n' + '='.repeat(60));
    console.log('📊 K- RECORDS FIX COMPLETE');
    console.log('='.repeat(60));
    console.log(`✅ Successfully fixed: ${results.success.length} K- records`);
    console.log(`❌ Failed: ${results.errors.length} K- records`);
    
    if (results.success.length > 0) {
      console.log('\n✅ SAMPLE FIXED K- RECORDS:');
      results.success.slice(0, 10).forEach(record => {
        console.log(`  - ${record.receiveNo}:`);
        console.log(`    Issue No: ${record.issueNo} | Carpet No: ${record.carpetNo}`);
        console.log(`    Rate: ₹${record.rate} | Area: ${record.area} | Amount: ₹${record.amount}`);
        console.log(`    Weaver: ${record.weaver}`);
        console.log('    ' + '-'.repeat(50));
      });
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.slice(0, 5).forEach(error => {
        console.log(`  - ${error.receiveNo}: ${error.error}`);
      });
    }
    
    // Verify K- records fix
    await verifyKRecordsFix();

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 K- RECORDS PROPERLY FIXED!');
    console.log('✅ Issue No = Carpet No = Receive No (all same)');
    console.log('✅ Proper rates calculated based on area/amount');
    console.log('✅ Clean weaver names preserved');
    console.log('✅ All data properly structured');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
