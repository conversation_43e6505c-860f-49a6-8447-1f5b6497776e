const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/carpet2024', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

async function fixStockMarchRate() {
  console.log('🔄 FIXING STOCK MARCH RATE TO ZERO');
  console.log('='.repeat(50));
  
  try {
    // Find only Stock March entries (not K- entries)
    const stockMarchEntries = await CarpetReceived.find({
      $or: [
        { receiveNo: { $regex: '^H-', $options: 'i' } },
        { receiveNo: { $regex: '^March', $options: 'i' } },
        { receiveNo: { $regex: '^Stock March', $options: 'i' } },
        { weaverName: { $regex: 'stock march', $options: 'i' } }
      ]
    });
    
    console.log(`📊 Found ${stockMarchEntries.length} Stock March entries`);
    
    let updatedCount = 0;
    
    for (const entry of stockMarchEntries) {
      const updateData = {};
      let needsUpdate = false;
      
      // Check main rate field
      if (entry.rate && entry.rate !== 0 && entry.rate !== '0') {
        updateData.rate = 0;
        needsUpdate = true;
      }
      
      // Check main amount field (keep it zero)
      if (entry.amount && entry.amount !== 0 && entry.amount !== '0') {
        updateData.amount = 0;
        needsUpdate = true;
      }
      
      // Check issueNo rate and amount
      if (entry.issueNo) {
        if (entry.issueNo.rate && entry.issueNo.rate !== '0') {
          updateData['issueNo.rate'] = '0';
          needsUpdate = true;
        }
        
        if (entry.issueNo.amount && entry.issueNo.amount !== '0') {
          updateData['issueNo.amount'] = '0';
          needsUpdate = true;
        }
      }
      
      if (needsUpdate) {
        await CarpetReceived.updateOne(
          { _id: entry._id },
          { $set: updateData }
        );
        
        updatedCount++;
        console.log(`✅ Updated: ${entry.receiveNo} - Rate & Amount set to 0`);
      } else {
        console.log(`⏭️  Skipped: ${entry.receiveNo} - Already has rate & amount as 0`);
      }
    }
    
    console.log(`🎉 Successfully updated ${updatedCount} entries out of ${stockMarchEntries.length} found`);
    console.log('✅ All Stock March entries now have rate = 0 and amount = 0');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the fix
fixStockMarchRate();
