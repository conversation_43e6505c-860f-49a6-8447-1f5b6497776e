import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';

interface WeaverData {
  _id: string;
  name: string;
  code: string;
  H: string;
  source: string; // 'master' or 'received'
}

interface AdvancedEntry {
  date: string;
  carpetNo?: string;
  cr: number;
  dr: number;
  balance: number;
  remark?: string;
}

interface BankAccount {
  _id: string;
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
}

@Component({
  selector: 'app-weaver-advanced',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './weaver-advanced.component.html',
  styleUrls: ['./weaver-advanced.component.css']
})
export class WeaverAdvancedComponent implements OnInit {
  advancedForm: FormGroup;
  paymentEntryForm: FormGroup;
  weavers: WeaverData[] = [];
  bankAccounts: BankAccount[] = [];
  advancedEntries: AdvancedEntry[] = [];
  isLoading = false;
  isSaving = false;
  showTable = false;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient
  ) {
    this.advancedForm = this.fb.group({
      weaver: ['', Validators.required],
      fromDate: ['', Validators.required],
      toDate: ['', Validators.required]
    });

    this.paymentEntryForm = this.fb.group({
      amount: ['', [Validators.required, Validators.min(0.01)]],
      bankAccount: ['', Validators.required],
      remarks: ['']
    });
  }

  ngOnInit(): void {
    this.loadWeavers();
    this.loadBankAccounts();
  }

  loadWeavers(): void {
    console.log('Loading weavers from both sources...');

    // Load from master (weaver employees)
    const masterWeavers$ = this.http.get<any[]>('http://localhost:2000/api/phase-three/weaver/employees');

    // Load from carpet received
    const receivedWeavers$ = this.http.get<any[]>('http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied');

    // Load master weavers first
    masterWeavers$.subscribe({
      next: (masterData) => {
        console.log('Master weavers loaded:', masterData);

        // Transform master data
        const masterWeavers: WeaverData[] = masterData.map(weaver => ({
          _id: weaver._id,
          name: weaver.name.startsWith('K-') ? weaver.name.substring(2) : weaver.name,
          code: 'K',
          H: '',
          source: 'master'
        }));

        // Load carpet received data
        receivedWeavers$.subscribe({
          next: (receivedData) => {
            console.log('Carpet received data loaded:', receivedData);

            // Extract unique weavers from carpet received data
            const uniqueReceivedWeavers = new Map();
            console.log('Sample carpet data:', receivedData.slice(0, 3)); // Debug first 3 records

            receivedData.forEach((carpet, index) => {
              // Try multiple ways to get weaver info
              let weaverId = null;
              let weaverName = null;

              if (carpet.weaverNumber && carpet.weaverNumber._id) {
                weaverId = carpet.weaverNumber._id;
                weaverName = carpet.weaverNumber.name;
              } else if (carpet.weaverNumber && typeof carpet.weaverNumber === 'string') {
                weaverId = carpet.weaverNumber;
                weaverName = carpet.weaverName || `Weaver-${carpet.weaverNumber}`;
              } else if (carpet.weaverName) {
                weaverId = carpet.weaverName.replace(/\s+/g, '_'); // Use name as ID
                weaverName = carpet.weaverName;
              } else if (carpet.receiveNo) {
                // Use receiveNo as fallback
                weaverId = `weaver_${carpet.receiveNo}`;
                weaverName = `Weaver ${carpet.receiveNo}`;
              }

              if (weaverId && weaverName && !uniqueReceivedWeavers.has(weaverId)) {
                uniqueReceivedWeavers.set(weaverId, {
                  _id: weaverId,
                  name: weaverName.startsWith('H-') ? weaverName.substring(2) : weaverName,
                  code: 'H',
                  H: carpet.H || carpet.quality || '',
                  source: 'received'
                });
              }

              // Debug first few records
              if (index < 5) {
                console.log(`Carpet ${index}:`, {
                  weaverNumber: carpet.weaverNumber,
                  weaverName: carpet.weaverName,
                  receiveNo: carpet.receiveNo,
                  extractedId: weaverId,
                  extractedName: weaverName
                });
              }
            });

            const receivedWeavers: WeaverData[] = Array.from(uniqueReceivedWeavers.values());

            // Combine both sources (avoid duplicates by name)
            const allWeavers = [...masterWeavers];
            receivedWeavers.forEach(receivedWeaver => {
              if (!allWeavers.find(w => w.name.toLowerCase() === receivedWeaver.name.toLowerCase())) {
                allWeavers.push(receivedWeaver);
              }
            });

            this.weavers = allWeavers;
            console.log('Combined weavers:', this.weavers);
          },
          error: (error) => {
            console.error('Error loading carpet received data:', error);
            // Use only master weavers if carpet data fails
            this.weavers = masterWeavers;
          }
        });
      },
      error: (error) => {
        console.error('Error loading master weavers:', error);
      }
    });
  }

  onFormChange(): void {
    const weaverId = this.advancedForm.get('weaver')?.value;
    const fromDate = this.advancedForm.get('fromDate')?.value;
    const toDate = this.advancedForm.get('toDate')?.value;

    if (weaverId && fromDate && toDate) {
      this.loadAdvancedData(weaverId, fromDate, toDate);
    } else {
      this.showTable = false;
      this.advancedEntries = [];
    }
  }

  loadAdvancedData(weaverId: string, fromDate: string, toDate: string): void {
    this.isLoading = true;
    
    // For now, we'll create sample data since advanced payments might not be implemented yet
    // In a real implementation, you would call an API endpoint for advanced payments
    setTimeout(() => {
      this.generateSampleAdvancedEntries();
      this.isLoading = false;
      this.showTable = true;
    }, 1000);
  }

  generateSampleAdvancedEntries(): void {
    // This is sample data - replace with actual API call
    this.advancedEntries = [
      {
        date: new Date().toISOString(),
        carpetNo: 'C001',
        cr: 0,
        dr: 5000,
        balance: -5000,
        remark: 'Advanced Payment'
      },
      {
        date: new Date(Date.now() - 86400000).toISOString(),
        carpetNo: 'C002',
        cr: 0,
        dr: 3000,
        balance: -8000,
        remark: 'Material Advance'
      }
    ];
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  }

  getTotalCR(): number {
    return this.advancedEntries.reduce((sum, entry) => sum + entry.cr, 0);
  }

  getTotalDR(): number {
    return this.advancedEntries.reduce((sum, entry) => sum + entry.dr, 0);
  }

  getFinalBalance(): number {
    return this.advancedEntries.length > 0 ? 
      this.advancedEntries[this.advancedEntries.length - 1].balance : 0;
  }

  getSelectedWeaverName(): string {
    const weaverId = this.advancedForm.get('weaver')?.value;
    const weaver = this.weavers.find(w => w._id === weaverId);
    return weaver ? weaver.name : '';
  }

  loadBankAccounts(): void {
    // Load bank accounts from received account API
    this.http.get<BankAccount[]>('http://localhost:2000/api/phase-four/receivedAccount/receivedAccount')
      .subscribe({
        next: (accounts) => {
          this.bankAccounts = accounts;
          console.log('Bank accounts loaded:', accounts.length);
        },
        error: (error) => {
          console.error('Error loading bank accounts:', error);
          // Fallback sample data
          this.bankAccounts = [
            { _id: '1', bankName: 'SBI', accountNumber: '********', accountHolderName: 'Company Account' },
            { _id: '2', bankName: 'HDFC', accountNumber: '********', accountHolderName: 'Business Account' }
          ];
        }
      });
  }

  savePaymentEntry(): void {
    if (this.paymentEntryForm.valid) {
      this.isSaving = true;

      const amount = this.paymentEntryForm.get('amount')?.value;
      const bankAccountId = this.paymentEntryForm.get('bankAccount')?.value;
      const remarks = this.paymentEntryForm.get('remarks')?.value;

      // Create new entry for the table
      const newEntry: AdvancedEntry = {
        date: new Date().toISOString(),
        carpetNo: '-', // No carpet number for payment entries
        cr: amount, // Payment goes to credit
        dr: 0,
        balance: this.calculateNewBalance(amount),
        remark: remarks || 'Payment Entry'
      };

      console.log('Adding payment entry:', newEntry);

      // Add to table
      this.advancedEntries.push(newEntry);

      // Recalculate all balances
      this.recalculateBalances();

      setTimeout(() => {
        this.isSaving = false;

        // Reset form
        this.paymentEntryForm.reset();

        // Show success message
        alert('Payment entry added successfully!');

      }, 500);
    }
  }

  calculateNewBalance(creditAmount: number): number {
    const currentBalance = this.advancedEntries.length > 0
      ? this.advancedEntries[this.advancedEntries.length - 1].balance
      : 0;
    return currentBalance + creditAmount;
  }

  recalculateBalances(): void {
    let runningBalance = 0;
    this.advancedEntries.forEach(entry => {
      runningBalance += entry.cr - entry.dr;
      entry.balance = runningBalance;
    });
  }

  addAdvancedPayment(): void {
    // This would open a modal or form to add new advanced payment
    console.log('Add advanced payment functionality to be implemented');
  }
}
