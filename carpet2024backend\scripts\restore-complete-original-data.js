// Script to restore complete original data exactly as it was before import
const mongoose = require('mongoose');
const fs = require('fs');

// Database connection
const DB_URL = "mongodb+srv://infosarthaktech:<EMAIL>/test";

async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000
    });
    console.log('✅ Connected to MongoDB Atlas');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

// Read original data from backup file
function getOriginalData() {
  try {
    const originalData = JSON.parse(fs.readFileSync('scripts/original-53-proper-data-results.json', 'utf8'));
    return originalData.success;
  } catch (error) {
    console.error('❌ Error reading original data file:', error);
    return null;
  }
}

async function restoreCompleteOriginalData() {
  console.log('🔄 RESTORING COMPLETE ORIGINAL DATA');
  console.log('='.repeat(60));
  
  try {
    const originalRecords = getOriginalData();
    if (!originalRecords) {
      console.error('❌ Could not load original data');
      return { success: [], errors: [] };
    }
    
    console.log(`📊 Found ${originalRecords.length} original records to restore`);
    
    const db = mongoose.connection.db;
    const collection = db.collection('carpetreceiveds');
    
    const results = { success: [], errors: [] };
    
    // Restore each record with original data
    for (let i = 0; i < originalRecords.length; i++) {
      try {
        const originalRecord = originalRecords[i];
        
        // Calculate rate based on original area and amount
        const areaValue = parseFloat(originalRecord.area.replace(' Ft', ''));
        const rate = Math.round(originalRecord.amount / areaValue);
        
        // Create proper issueNo object with original data
        const issueNoObject = {
          Br_issueNo: originalRecord.issueNo,
          date: new Date(originalRecord.date).toISOString(),
          quality: { quality: originalRecord.quality },
          design: { design: originalRecord.design },
          borderColour: 'Cream/Brown',
          size: {
            sizeInYard: '2.3x4.5',
            sizeinMeter: '2.3x4.5'
          },
          rate: rate.toString(),
          amount: originalRecord.amount.toString(),
          areaIn: 'Sq.Ft'
        };

        // Update the record with complete original data
        await collection.updateOne(
          { receiveNo: originalRecord.receiveNo },
          { 
            $set: { 
              receivingDate: new Date(originalRecord.date),
              issueNo: issueNoObject,
              area: originalRecord.area,
              amount: originalRecord.amount.toString(),
              pcs: 1,
              weaverName: originalRecord.weaver, // Original weaver names
              quality: originalRecord.quality, // Original quality
              design: originalRecord.design, // Original design
              colour: 'Cream',
              colour2: 'Brown',
              size: '23 X 45',
              carpetNo: originalRecord.receiveNo
            } 
          }
        );
        
        results.success.push({
          receiveNo: originalRecord.receiveNo,
          issueNo: originalRecord.issueNo,
          design: originalRecord.design,
          quality: originalRecord.quality,
          weaver: originalRecord.weaver,
          area: originalRecord.area,
          amount: originalRecord.amount,
          rate: rate
        });
        
        if ((i + 1) % 10 === 0) {
          console.log(`✅ Restored ${i + 1}/${originalRecords.length} records`);
        }
        
      } catch (error) {
        console.error(`❌ Error restoring record ${i + 1}:`, error.message);
        results.errors.push({
          receiveNo: originalRecords[i]?.receiveNo || `Record ${i + 1}`,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error restoring original data:', error);
    return { success: [], errors: [] };
  }
}

async function verifyOriginalDataRestore() {
  console.log('\n🔍 Verifying original data restore...');
  
  try {
    const db = mongoose.connection.db;
    
    // Get sample restored records
    const samples = await db.collection('carpetreceiveds').find({ receiveNo: { $regex: /^K-/ } }).limit(10).toArray();
    
    console.log('\n📋 Sample restored records with original data:');
    samples.forEach((record, index) => {
      const issueNo = record.issueNo?.Br_issueNo || 'N/A';
      const rate = record.issueNo?.rate || 'N/A';
      
      console.log(`${index + 1}. ${record.receiveNo}`);
      console.log(`   Issue No: ${issueNo}`);
      console.log(`   Design: ${record.design || 'N/A'} (Original)`);
      console.log(`   Quality: ${record.quality || 'N/A'} (Original)`);
      console.log(`   Weaver: ${record.weaverName || 'N/A'} (Original)`);
      console.log(`   Rate: ₹${rate}/Sq.Ft`);
      console.log(`   Area: ${record.area || 'N/A'}`);
      console.log(`   Amount: ₹${record.amount || 'N/A'}`);
      console.log(`   Date: ${record.receivingDate?.toISOString()?.split('T')[0] || 'N/A'}`);
      console.log('   ' + '-'.repeat(60));
    });
    
    // Check distinct designs
    const designs = await db.collection('carpetreceiveds').distinct('design', { receiveNo: { $regex: /^K-/ } });
    console.log(`\n🎨 Original Designs Restored (${designs.length}):`);
    designs.forEach((design, index) => {
      console.log(`   ${index + 1}. ${design}`);
    });
    
    // Check distinct qualities
    const qualities = await db.collection('carpetreceiveds').distinct('quality', { receiveNo: { $regex: /^K-/ } });
    console.log(`\n⭐ Original Qualities Restored (${qualities.length}):`);
    qualities.forEach((quality, index) => {
      console.log(`   ${index + 1}. ${quality}`);
    });
    
    // Check distinct weavers
    const weavers = await db.collection('carpetreceiveds').distinct('weaverName', { receiveNo: { $regex: /^K-/ } });
    console.log(`\n👥 Original Weavers Restored (${weavers.length}):`);
    weavers.forEach((weaver, index) => {
      console.log(`   ${index + 1}. ${weaver}`);
    });
    
  } catch (error) {
    console.error('❌ Error verifying original data restore:', error);
  }
}

async function main() {
  console.log('🔄 RESTORING COMPLETE ORIGINAL DATA');
  console.log('(Exactly as it was before any imports)');
  console.log('='.repeat(60));
  
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) return;

    // Restore complete original data
    const results = await restoreCompleteOriginalData();

    // Display results
    console.log('\n' + '='.repeat(60));
    console.log('📊 COMPLETE ORIGINAL DATA RESTORATION');
    console.log('='.repeat(60));
    console.log(`✅ Successfully restored: ${results.success.length} records`);
    console.log(`❌ Failed: ${results.errors.length} records`);
    
    if (results.success.length > 0) {
      console.log('\n✅ SAMPLE RESTORED RECORDS:');
      results.success.slice(0, 10).forEach(record => {
        console.log(`  - ${record.receiveNo}:`);
        console.log(`    Design: ${record.design} | Quality: ${record.quality}`);
        console.log(`    Weaver: ${record.weaver} | Rate: ₹${record.rate}/Sq.Ft`);
        console.log(`    Area: ${record.area} | Amount: ₹${record.amount}`);
        console.log('    ' + '-'.repeat(50));
      });
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.slice(0, 5).forEach(error => {
        console.log(`  - ${error.receiveNo}: ${error.error}`);
      });
    }
    
    // Verify original data restore
    await verifyOriginalDataRestore();

  } catch (error) {
    console.error('❌ Restoration failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    
    console.log('\n🎉 COMPLETE ORIGINAL DATA RESTORED!');
    console.log('✅ All records restored to exact pre-import state');
    console.log('✅ Original designs: Kamaro, Sonam, Bhaktiri, Isfahan, etc.');
    console.log('✅ Original qualities: 9x54, 10x60, 8x48, etc.');
    console.log('✅ Original weavers: Shabana, Amit Singh, Rajesh Gupta, etc.');
    console.log('✅ Proper rates calculated from original area/amount');
    console.log('✅ Issue No = Carpet No = Receive No');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
