const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/carpet2024', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Use the actual model
const CarpetReceived = require('./src/model/phase-4/carpetReceived');

// Format size function - keep neat and clean format like "7 X 42"
const formatSize = (size) => {
  if (!size) return size;
  
  // Handle patterns like "5 6 x 7 9" -> "5.6 X 7.9"
  const spacedPattern = /(\d+)\s+(\d+)\s*[Xx]\s*(\d+)\s+(\d+)/;
  const spacedMatch = size.match(spacedPattern);
  
  if (spacedMatch) {
    const width1 = spacedMatch[1];
    const width2 = spacedMatch[2];
    const length1 = spacedMatch[3];
    const length2 = spacedMatch[4];
    
    return `${width1}.${width2} X ${length1}.${length2}`;
  }
  
  // Handle normal patterns and ensure proper spacing like "7 X 42"
  const sizePattern = /(\d+(?:\.\d+)?)\s*[Xx]\s*(\d+(?:\.\d+)?)/;
  const match = size.match(sizePattern);
  
  if (match) {
    const width = match[1];
    const length = match[2];
    
    // Ensure neat spacing: single space before and after X
    return `${width} X ${length}`;
  }
  
  return size;
};

// Function to add decimal to multi-digit numbers
const addDecimalToMultiDigit = (num) => {
  if (num.length <= 1) return num;
  return num.charAt(0) + '.' + num.slice(1);
};

async function updateSizeFields() {
  console.log('🔄 UPDATING SIZE IN YARD AND SIZE IN METER FIELDS');
  console.log('='.repeat(70));
  
  try {
    // Get all carpet received entries
    const allEntries = await CarpetReceived.find({});
    console.log(`📊 Found ${allEntries.length} total entries`);
    
    let updatedCount = 0;
    
    for (const entry of allEntries) {
      let needsUpdate = false;
      const updateData = {};
      
      // Check main size field
      if (entry.size) {
        const newSize = formatSize(entry.size);
        if (entry.size !== newSize) {
          updateData.size = newSize;
          needsUpdate = true;
        }
      }
      
      // Check issueNo object
      if (entry.issueNo && entry.issueNo.size) {
        const currentSizeInYard = entry.issueNo.size.sizeInYard;
        const currentSizeInMeter = entry.issueNo.size.sizeinMeter || entry.issueNo.size.sizeInMeter;
        
        if (currentSizeInYard) {
          const newSizeInYard = formatSize(currentSizeInYard);
          if (currentSizeInYard !== newSizeInYard) {
            updateData['issueNo.size.sizeInYard'] = newSizeInYard;
            needsUpdate = true;
          }
        }
        
        if (currentSizeInMeter) {
          const newSizeInMeter = formatSize(currentSizeInMeter);
          if (currentSizeInMeter !== newSizeInMeter) {
            updateData['issueNo.size.sizeinMeter'] = newSizeInMeter;
            needsUpdate = true;
          }
        }
        
        // Set rate and amount to zero
        if (entry.issueNo.rate && entry.issueNo.rate !== '0') {
          updateData['issueNo.rate'] = '0';
          needsUpdate = true;
        }
        
        if (entry.issueNo.amount && entry.issueNo.amount !== '0') {
          updateData['issueNo.amount'] = '0';
          needsUpdate = true;
        }
      }
      
      // Set main rate and amount to zero
      if (entry.rate && entry.rate !== 0 && entry.rate !== '0') {
        updateData.rate = 0;
        needsUpdate = true;
      }
      
      if (entry.amount && entry.amount !== 0 && entry.amount !== '0') {
        updateData.amount = 0;
        needsUpdate = true;
      }
      
      if (needsUpdate) {
        await CarpetReceived.updateOne(
          { _id: entry._id },
          { $set: updateData }
        );
        
        updatedCount++;
        console.log(`✅ Updated: ${entry.receiveNo} - Size fields formatted, Rate & Amount set to 0`);
      }
    }
    
    console.log(`🎉 Successfully updated ${updatedCount} entries out of ${allEntries.length} found`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the update
updateSizeFields();
