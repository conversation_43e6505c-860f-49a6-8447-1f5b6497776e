const express = require('express');
const carpetReceivedController = require('../../controller/manifacturing/carpetRecevied-controller');


const router = express.Router();

router.post('/carpetRecevied/',  carpetReceivedController.createCarpetReceived);
router.get('/carpetRecevied/', carpetReceivedController.getAllCarpetReceiveds);

// Get received carpets by issue IDs (must come before /:id route)
router.post('/carpetRecevied/by-issues', carpetReceivedController.getReceivedCarpetsByIssueIds);
router.get('/carpetRecevied/test-by-issues', (req, res) => {
  res.json({ message: 'Test route working', timestamp: new Date() });
});

router.get('/carpetRecevied/:id', carpetReceivedController.getCarpetReceivedById);
router.put('/carpetRecevied/:id', carpetReceivedController.updateCarpetReceived);
router.delete('/carpetRecevied/:id', carpetReceivedController.deleteCarpetReceived);

module.exports = router;
